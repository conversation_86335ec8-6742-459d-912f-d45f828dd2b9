"""Initial migration

Revision ID: a7d532215a15
Revises: 
Create Date: 2025-02-17 16:04:13.262630

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a7d532215a15'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_refresh_tokens_id', table_name='refresh_tokens')
    op.drop_table('refresh_tokens')
    op.drop_index('ix_job_applications_id', table_name='job_applications')
    op.drop_table('job_applications')
    op.drop_index('ix_work_experiences_id', table_name='work_experiences')
    op.drop_table('work_experiences')
    op.drop_index('ix_users_email', table_name='users')
    op.drop_index('ix_users_id', table_name='users')
    op.drop_index('ix_users_phone_number', table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('users_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('username', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('email', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('password', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('full_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('phone_number', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('role', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('profile_picture', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='users_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_users_phone_number', 'users', ['phone_number'], unique=True)
    op.create_index('ix_users_id', 'users', ['id'], unique=False)
    op.create_index('ix_users_email', 'users', ['email'], unique=True)
    op.create_table('work_experiences',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('job_application_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('country', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('position', postgresql.ENUM('houseMaid', 'babysitter', 'cooker', 'driver', 'others', name='jobposition'), autoincrement=False, nullable=False),
    sa.Column('other_job', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('experience_others', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('duration', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['job_application_id'], ['job_applications.id'], name='work_experiences_job_application_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='work_experiences_pkey')
    )
    op.create_index('ix_work_experiences_id', 'work_experiences', ['id'], unique=False)
    op.create_table('job_applications',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('full_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('job_applied', postgresql.ENUM('houseMaid', 'babysitter', 'cooker', 'driver', 'others', name='jobposition'), autoincrement=False, nullable=False),
    sa.Column('other_job', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('contract_time', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('monthly_salary', sa.NUMERIC(), autoincrement=False, nullable=False),
    sa.Column('gender', postgresql.ENUM('Male', 'Female', name='gender_enum'), autoincrement=False, nullable=False),
    sa.Column('complexion', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('civil_status', postgresql.ENUM('single', 'married', 'divorced', 'widowed', name='civilstatus'), autoincrement=False, nullable=False),
    sa.Column('emergency_contact_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('emergency_contact_phone', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('latest_education', postgresql.ENUM('primarySchool', 'highSchool', 'college', 'additionalCourse', name='educationlevel'), autoincrement=False, nullable=False),
    sa.Column('education_others', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('passport_no', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('place_of_issue', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('date_of_issue', sa.DATE(), autoincrement=False, nullable=False),
    sa.Column('date_of_expire', sa.DATE(), autoincrement=False, nullable=False),
    sa.Column('nationality', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('religion', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('date_of_birth', sa.DATE(), autoincrement=False, nullable=False),
    sa.Column('age', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('place_of_birth', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('home_address', sa.VARCHAR(length=500), autoincrement=False, nullable=False),
    sa.Column('contact_no', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('children', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('weight', sa.NUMERIC(), autoincrement=False, nullable=False),
    sa.Column('height', sa.NUMERIC(), autoincrement=False, nullable=False),
    sa.Column('speaks_english', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('speaks_arabic', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('speaks_other_languages', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_cleaning', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_washing', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_ironing', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_cooking', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_baby_sitting', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_children_care', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('skill_elder_care', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('other_skills', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('remarks', sa.VARCHAR(length=500), autoincrement=False, nullable=True),
    sa.Column('name_signature', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('application_status', postgresql.ENUM('pending', 'finalized', 'hired', 'government_registered', 'rejected', name='applicationstatus'), autoincrement=False, nullable=False),
    sa.Column('missing_files_status', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('coc_verification_status', postgresql.ENUM('pending', 'not_available', 'available', 'approved_by_staff', name='cocverificationstatus'), autoincrement=False, nullable=False),
    sa.Column('visa_status', postgresql.ENUM('pending', 'approved', 'rejected', name='visastatus'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('submit_to', postgresql.ENUM('admin', 'manager', name='submissionrole'), autoincrement=False, nullable=False),
    sa.Column('photo_uri', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('passport_photo_uri', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('medical_photo_uri', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('educational_photo_uri', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('work_experience_photo_uri', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('coc_photo_uri', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='job_applications_pkey'),
    sa.UniqueConstraint('passport_no', name='job_applications_passport_no_key')
    )
    op.create_index('ix_job_applications_id', 'job_applications', ['id'], unique=False)
    op.create_table('refresh_tokens',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('token', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='refresh_tokens_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='refresh_tokens_pkey'),
    sa.UniqueConstraint('token', name='refresh_tokens_token_key')
    )
    op.create_index('ix_refresh_tokens_id', 'refresh_tokens', ['id'], unique=False)
    # ### end Alembic commands ###
